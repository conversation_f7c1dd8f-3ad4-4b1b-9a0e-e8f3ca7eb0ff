document.addEventListener('DOMContentLoaded', function() {
    // 初始化标签页
    if (typeof bootstrap !== 'undefined') {
        console.log('使用Bootstrap初始化标签页');
        const triggerTabList = document.querySelectorAll('#queryTabs button');
        triggerTabList.forEach(triggerEl => {
            const tabTrigger = new bootstrap.Tab(triggerEl);
            triggerEl.addEventListener('click', event => {
                event.preventDefault();
                tabTrigger.show();
            });
        });
    } else {
        console.error('Bootstrap未加载，无法初始化标签页');
        // 尝试手动加载Bootstrap
        const script = document.createElement('script');
        script.src = 'https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js';
        script.onload = function() {
            console.log("Bootstrap已手动加载成功");
            // 初始化标签页
            const triggerTabList = document.querySelectorAll('#queryTabs button');
            triggerTabList.forEach(triggerEl => {
                const tabTrigger = new bootstrap.Tab(triggerEl);
                triggerEl.addEventListener('click', event => {
                    event.preventDefault();
                    tabTrigger.show();
                });
            });
        };
        document.head.appendChild(script);
    }
    
    // 获取应用根路径
    function getAppRoot() {
        // 从当前URL中获取应用根路径
        const pathParts = window.location.pathname.split('/');
        if (pathParts.length >= 2 && pathParts[1] === 'chaxun') {
            return '/chaxun';
        }
        return '';
    }
    
    const APP_ROOT = getAppRoot();
    console.log('应用根路径:', APP_ROOT);
    
    // 全局变量
    const pageSize = 20;
    let currentApiPage = 1;
    let currentCookiePage = 1;
    let totalApiRecords = 0;
    let totalCookieRecords = 0;
    let currentApiKey = '';
    
    // 获取DOM元素
    // API Key查询选项卡
    const apiKeyInput = document.getElementById('apiKeyInput');
    const apiKeyQueryBtn = document.getElementById('apiKeyQueryBtn');
    const apiInfoTable = document.getElementById('apiInfoTable');
    const usageTable = document.getElementById('usageTable');
    const manageThisKeyBtn = document.getElementById('manageThisKeyBtn');
    const apiPrevBtn = document.getElementById('apiPrevBtn');
    const apiNextBtn = document.getElementById('apiNextBtn');
    const apiCurrentPage = document.getElementById('apiCurrentPage');
    const apiTotalPages = document.getElementById('apiTotalPages');
    const apiPageSize = document.getElementById('apiPageSize');
    
    // Cookie查询选项卡
    const cookieInput = document.getElementById('cookieInput');
    const cookieQueryBtn = document.getElementById('cookieQueryBtn');
    const cookieUsageTable = document.getElementById('cookieUsageTable');
    const cookiePrevBtn = document.getElementById('cookiePrevBtn');
    const cookieNextBtn = document.getElementById('cookieNextBtn');
    const cookieCurrentPage = document.getElementById('cookieCurrentPage');
    const cookieTotalPages = document.getElementById('cookieTotalPages');
    const cookiePageSize = document.getElementById('cookiePageSize');
    
    // API Key管理选项卡
    const manageKeyInput = document.getElementById('manageKeyInput');
    const manageKeyQueryBtn = document.getElementById('manageKeyQueryBtn');
    const infoTotalQuota = document.getElementById('infoTotalQuota');
    const infoRemaining = document.getElementById('infoRemaining');
    const infoDailyLimit = document.getElementById('infoDailyLimit');
    const infoCreatedAt = document.getElementById('infoCreatedAt');
    const infoFirstUsedAt = document.getElementById('infoFirstUsedAt');
    const infoExpiresAt = document.getElementById('infoExpiresAt');
    const infoLastCookieRequest = document.getElementById('infoLastCookieRequest');
    const infoIsActive = document.getElementById('infoIsActive');
    const addQuotaInput = document.getElementById('addQuotaInput');
    const addQuotaBtn = document.getElementById('addQuotaBtn');
    const dailyLimitInput = document.getElementById('dailyLimitInput');
    const setDailyLimitBtn = document.getElementById('setDailyLimitBtn');
    const extendDaysInput = document.getElementById('extendDaysInput');
    const extendHoursInput = document.getElementById('extendHoursInput');
    const extendMinutesInput = document.getElementById('extendMinutesInput');
    const extendExpiryBtn = document.getElementById('extendExpiryBtn');
    const extendPreview = document.getElementById('extendPreview');
    const extendPreviewText = document.getElementById('extendPreviewText');
    const toggleActiveBtn = document.getElementById('toggleActiveBtn');
    const clearLastRequestBtn = document.getElementById('clearLastRequestBtn');
    
    // 状态消息
    const statusMessage = document.getElementById('statusMessage');
    
    // 绑定事件
    // API Key查询
    apiKeyInput.addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            queryApiKey();
        }
    });
    apiKeyQueryBtn.addEventListener('click', queryApiKey);
    manageThisKeyBtn.addEventListener('click', switchToManage);
    apiPrevBtn.addEventListener('click', () => changeApiPage(-1));
    apiNextBtn.addEventListener('click', () => changeApiPage(1));
    apiCurrentPage.addEventListener('change', onApiPageChanged);
    apiPageSize.addEventListener('change', onApiPageSizeChanged);
    
    // Cookie查询
    cookieInput.addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            queryCookie();
        }
    });
    cookieQueryBtn.addEventListener('click', queryCookie);
    cookiePrevBtn.addEventListener('click', () => changeCookiePage(-1));
    cookieNextBtn.addEventListener('click', () => changeCookiePage(1));
    cookieCurrentPage.addEventListener('change', onCookiePageChanged);
    cookiePageSize.addEventListener('change', onCookiePageSizeChanged);
    
    // API Key管理
    manageKeyInput.addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            queryManageKey();
        }
    });
    manageKeyQueryBtn.addEventListener('click', queryManageKey);
    addQuotaBtn.addEventListener('click', addQuota);
    setDailyLimitBtn.addEventListener('click', setDailyLimit);
    extendExpiryBtn.addEventListener('click', extendExpiry);
    toggleActiveBtn.addEventListener('click', toggleActive);
    clearLastRequestBtn.addEventListener('click', clearLastRequestTime);

    // 延长有效期预览更新
    extendDaysInput.addEventListener('input', updateExtendPreview);
    extendHoursInput.addEventListener('input', updateExtendPreview);
    extendMinutesInput.addEventListener('input', updateExtendPreview);
    
    // 选项卡切换事件
    const queryTabs = document.getElementById('queryTabs');
    const tabElements = queryTabs.querySelectorAll('button[data-bs-toggle="tab"]');
    tabElements.forEach(tab => {
        tab.addEventListener('shown.bs.tab', function(e) {
            const targetId = e.target.getAttribute('id');
            if (targetId === 'api-key-tab') {
                apiKeyInput.focus();
            } else if (targetId === 'cookie-tab') {
                cookieInput.focus();
            } else if (targetId === 'manage-tab') {
                manageKeyInput.focus();
            }
        });
    });
    
    // 函数定义
    // API Key查询
    function queryApiKey() {
        const apiKey = apiKeyInput.value.trim();
        if (!apiKey) {
            showMessage('请输入API Key', 'warning');
            return;
        }
        
        currentApiKey = apiKey;
        
        // 显示加载状态
        showStatus('正在查询...');
        
        // 发送请求
        fetch(`${APP_ROOT}/api/query/api-key?key=${encodeURIComponent(apiKey)}&page=${currentApiPage}&page_size=${apiPageSize.value}`)
            .then(response => {
                if (!response.ok) {
                    throw new Error('查询失败');
                }
                return response.json();
            })
            .then(data => {
                if (data.error) {
                    showMessage(data.error, 'warning');
                    return;
                }
                
                // 更新基本信息表格
                updateApiInfoTable(data.info);
                
                // 更新使用记录表格
                updateUsageTable(data.usage);
                
                // 更新分页信息
                totalApiRecords = data.total_records;
                updateApiPagination();
                
                // 启用管理按钮
                manageThisKeyBtn.disabled = false;
                
                showStatus('查询完成');
            })
            .catch(error => {
                showMessage(`查询失败: ${error.message}`, 'danger');
                showStatus('查询失败');
            });
    }
    
    function updateApiInfoTable(info) {
        // 清空表格
        const tbody = apiInfoTable.querySelector('tbody');
        tbody.innerHTML = '';
        
        // 添加信息行
        const infoItems = [
            { key: '总配额', value: info.total_quota },
            { key: '剩余配额', value: info.remaining },
            { key: '状态', value: info.is_active ? '激活' : '禁用' },
            { key: '创建时间', value: convertToChineseTime(info.created_at) + ' (中国时间)' },
            { key: '激活时间', value: info.first_used_at ? convertToChineseTime(info.first_used_at) + ' (中国时间)' : '此卡未激活，等待首次使用' },
            { key: '过期时间', value: convertToChineseTime(info.expires_at) + ' (中国时间)' },
            { key: '每日限制', value: info.daily_limit },
            { key: '已使用', value: info.total_quota - info.remaining }
        ];
        
        infoItems.forEach(item => {
            const row = document.createElement('tr');
            
            const keyCell = document.createElement('td');
            keyCell.textContent = item.key;
            
            const valueCell = document.createElement('td');
            valueCell.textContent = item.value;
            
            row.appendChild(keyCell);
            row.appendChild(valueCell);
            tbody.appendChild(row);
        });
    }
    
    function updateUsageTable(usageRecords) {
        // 清空表格
        const tbody = usageTable.querySelector('tbody');
        tbody.innerHTML = '';
        
        if (usageRecords.length === 0) {
            // 添加空行
            const row = document.createElement('tr');
            const cell = document.createElement('td');
            cell.colSpan = 3;
            cell.textContent = '暂无使用记录';
            cell.style.textAlign = 'center';
            row.appendChild(cell);
            tbody.appendChild(row);
            return;
        }
        
        // 添加记录行
        usageRecords.forEach(record => {
            const row = document.createElement('tr');
            
            const timestampCell = document.createElement('td');
            timestampCell.textContent = convertToChineseTime(record.timestamp) + ' (中国时间)';
            
            const cookieCell = document.createElement('td');
            cookieCell.textContent = record.cookie;
            
            const ipCell = document.createElement('td');
            ipCell.textContent = record.ip;
            
            row.appendChild(timestampCell);
            row.appendChild(cookieCell);
            row.appendChild(ipCell);
            tbody.appendChild(row);
        });
    }
    
    function updateApiPagination() {
        const totalPages = Math.max(1, Math.ceil(totalApiRecords / apiPageSize.value));
        apiTotalPages.textContent = `/ ${totalPages}`;
        apiCurrentPage.max = totalPages;
        
        // 更新按钮状态
        apiPrevBtn.disabled = currentApiPage <= 1;
        apiNextBtn.disabled = currentApiPage >= totalPages;
    }
    
    function changeApiPage(delta) {
        const newPage = currentApiPage + delta;
        const totalPages = Math.max(1, Math.ceil(totalApiRecords / apiPageSize.value));
        
        if (newPage >= 1 && newPage <= totalPages) {
            currentApiPage = newPage;
            apiCurrentPage.value = newPage;
            queryApiKey();
        }
    }
    
    function onApiPageChanged() {
        const newPage = parseInt(apiCurrentPage.value);
        const totalPages = Math.max(1, Math.ceil(totalApiRecords / apiPageSize.value));
        
        if (newPage >= 1 && newPage <= totalPages) {
            currentApiPage = newPage;
            queryApiKey();
        }
    }
    
    function onApiPageSizeChanged() {
        currentApiPage = 1;
        apiCurrentPage.value = 1;
        queryApiKey();
    }
    
    // Cookie查询
    function queryCookie() {
        const cookie = cookieInput.value.trim();
        if (!cookie) {
            showMessage('请输入Cookie', 'warning');
            return;
        }
        
        // 显示加载状态
        showStatus('正在查询...');
        
        // 发送请求
        fetch(`${APP_ROOT}/api/query/cookie?cookie=${encodeURIComponent(cookie)}&page=${currentCookiePage}&page_size=${cookiePageSize.value}`)
            .then(response => {
                if (!response.ok) {
                    throw new Error('查询失败');
                }
                return response.json();
            })
            .then(data => {
                if (data.error) {
                    showMessage(data.error, 'warning');
                    return;
                }
                
                // 更新使用记录表格
                updateCookieUsageTable(data.usage);
                
                // 更新分页信息
                totalCookieRecords = data.total_records;
                updateCookiePagination();
                
                showStatus('查询完成');
            })
            .catch(error => {
                showMessage(`查询失败: ${error.message}`, 'danger');
                showStatus('查询失败');
            });
    }
    
    function updateCookieUsageTable(usageRecords) {
        // 清空表格
        const tbody = cookieUsageTable.querySelector('tbody');
        tbody.innerHTML = '';
        
        if (usageRecords.length === 0) {
            // 添加空行
            const row = document.createElement('tr');
            const cell = document.createElement('td');
            cell.colSpan = 4;
            cell.textContent = '暂无使用记录';
            cell.style.textAlign = 'center';
            row.appendChild(cell);
            tbody.appendChild(row);
            return;
        }
        
        // 添加记录行
        usageRecords.forEach(record => {
            const row = document.createElement('tr');
            
            const timestampCell = document.createElement('td');
            timestampCell.textContent = convertToChineseTime(record.timestamp) + ' (中国时间)';
            
            const apiKeyCell = document.createElement('td');
            apiKeyCell.textContent = record.api_key;
            
            const ipCell = document.createElement('td');
            ipCell.textContent = record.ip;
            
            const actionCell = document.createElement('td');
            const viewButton = document.createElement('button');
            viewButton.className = 'btn btn-sm btn-outline-primary';
            viewButton.textContent = '查看详情';
            viewButton.addEventListener('click', () => switchToApiKey(record.api_key));
            actionCell.appendChild(viewButton);
            
            row.appendChild(timestampCell);
            row.appendChild(apiKeyCell);
            row.appendChild(ipCell);
            row.appendChild(actionCell);
            tbody.appendChild(row);
        });
    }
    
    function updateCookiePagination() {
        const totalPages = Math.max(1, Math.ceil(totalCookieRecords / cookiePageSize.value));
        cookieTotalPages.textContent = `/ ${totalPages}`;
        cookieCurrentPage.max = totalPages;
        
        // 更新按钮状态
        cookiePrevBtn.disabled = currentCookiePage <= 1;
        cookieNextBtn.disabled = currentCookiePage >= totalPages;
    }
    
    function changeCookiePage(delta) {
        const newPage = currentCookiePage + delta;
        const totalPages = Math.max(1, Math.ceil(totalCookieRecords / cookiePageSize.value));
        
        if (newPage >= 1 && newPage <= totalPages) {
            currentCookiePage = newPage;
            cookieCurrentPage.value = newPage;
            queryCookie();
        }
    }
    
    function onCookiePageChanged() {
        const newPage = parseInt(cookieCurrentPage.value);
        const totalPages = Math.max(1, Math.ceil(totalCookieRecords / cookiePageSize.value));
        
        if (newPage >= 1 && newPage <= totalPages) {
            currentCookiePage = newPage;
            queryCookie();
        }
    }
    
    function onCookiePageSizeChanged() {
        currentCookiePage = 1;
        cookieCurrentPage.value = 1;
        queryCookie();
    }
    
    // API Key管理
    function queryManageKey() {
        const apiKey = manageKeyInput.value.trim();
        if (!apiKey) {
            showMessage('请输入API Key', 'warning');
            return;
        }
        
        // 显示加载状态
        showStatus('正在查询...');
        
        // 发送请求
        fetch(`${APP_ROOT}/api/query/manage-key?key=${encodeURIComponent(apiKey)}`)
            .then(response => {
                if (!response.ok) {
                    throw new Error('查询失败');
                }
                return response.json();
            })
            .then(data => {
                if (data.error) {
                    showMessage(data.error, 'warning');
                    return;
                }
                
                // 更新信息显示
                infoTotalQuota.textContent = data.total_quota;
                infoRemaining.textContent = data.remaining;
                infoDailyLimit.textContent = data.daily_limit;
                infoCreatedAt.textContent = convertToChineseTime(data.created_at) + ' (中国时间)';
                infoFirstUsedAt.textContent = data.first_used_at ? convertToChineseTime(data.first_used_at) + ' (中国时间)' : '此卡未激活，等待首次使用';
                infoExpiresAt.textContent = convertToChineseTime(data.expires_at) + ' (中国时间)';
                infoLastCookieRequest.textContent = data.last_cookie_request_at ? convertToChineseTime(data.last_cookie_request_at) + ' (中国时间)' : '从未换号';
                infoIsActive.textContent = data.is_active ? '激活' : '禁用';
                
                // 启用按钮
                addQuotaBtn.disabled = false;
                setDailyLimitBtn.disabled = false;
                extendExpiryBtn.disabled = false;
                toggleActiveBtn.disabled = false;
                clearLastRequestBtn.disabled = false;

                // 清空延长有效期输入框并更新预览
                extendDaysInput.value = '';
                extendHoursInput.value = '';
                extendMinutesInput.value = '';
                updateExtendPreview();

                showStatus('查询完成');
            })
            .catch(error => {
                showMessage(`查询失败: ${error.message}`, 'danger');
                showStatus('查询失败');
            });
    }
    
    function addQuota() {
        const apiKey = manageKeyInput.value.trim();
        const quota = addQuotaInput.value.trim();
        
        if (!apiKey) {
            showMessage('请输入API Key', 'warning');
            return;
        }
        
        if (!quota || isNaN(quota) || parseInt(quota) <= 0) {
            showMessage('请输入有效的配额数量', 'warning');
            return;
        }
        
        // 显示加载状态
        showStatus('正在增加配额...');
        
        // 发送请求
        fetch(`${APP_ROOT}/api/manage/add-quota`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                key: apiKey,
                quota: parseInt(quota)
            })
        })
            .then(response => {
                if (!response.ok) {
                    throw new Error('操作失败');
                }
                return response.json();
            })
            .then(data => {
                if (data.error) {
                    showMessage(data.error, 'warning');
                    return;
                }
                
                showMessage(`成功增加配额 ${quota}`, 'success');
                addQuotaInput.value = '';
                
                // 刷新显示
                queryManageKey();
            })
            .catch(error => {
                showMessage(`操作失败: ${error.message}`, 'danger');
                showStatus('操作失败');
            });
    }
    
    function setDailyLimit() {
        const apiKey = manageKeyInput.value.trim();
        const limit = dailyLimitInput.value.trim();
        
        if (!apiKey) {
            showMessage('请输入API Key', 'warning');
            return;
        }
        
        if (!limit || isNaN(limit) || parseInt(limit) < 0) {
            showMessage('请输入有效的每日限制', 'warning');
            return;
        }
        
        // 显示加载状态
        showStatus('正在设置每日限制...');
        
        // 发送请求
        fetch(`${APP_ROOT}/api/manage/set-daily-limit`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                key: apiKey,
                limit: parseInt(limit)
            })
        })
            .then(response => {
                if (!response.ok) {
                    throw new Error('操作失败');
                }
                return response.json();
            })
            .then(data => {
                if (data.error) {
                    showMessage(data.error, 'warning');
                    return;
                }
                
                showMessage(`成功设置每日限制为 ${limit}`, 'success');
                dailyLimitInput.value = '';
                
                // 刷新显示
                queryManageKey();
            })
            .catch(error => {
                showMessage(`操作失败: ${error.message}`, 'danger');
                showStatus('操作失败');
            });
    }
    
    function extendExpiry() {
        const apiKey = manageKeyInput.value.trim();
        const days = parseInt(extendDaysInput.value) || 0;
        const hours = parseInt(extendHoursInput.value) || 0;
        const minutes = parseInt(extendMinutesInput.value) || 0;

        if (!apiKey) {
            showMessage('请输入API Key', 'warning');
            return;
        }

        if (days === 0 && hours === 0 && minutes === 0) {
            showMessage('请至少输入一个时间值', 'warning');
            return;
        }

        // 显示加载状态
        showStatus('正在延长有效期...');

        // 发送请求
        fetch(`${APP_ROOT}/api/manage/extend-expiry`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                key: apiKey,
                days: days,
                hours: hours,
                minutes: minutes
            })
        })
            .then(response => {
                if (!response.ok) {
                    throw new Error('操作失败');
                }
                return response.json();
            })
            .then(data => {
                if (data.error) {
                    showMessage(data.error, 'warning');
                    return;
                }

                showMessage(data.message, 'success');
                extendDaysInput.value = '';
                extendHoursInput.value = '';
                extendMinutesInput.value = '';
                updateExtendPreview(); // 清空预览

                // 刷新显示
                queryManageKey();
            })
            .catch(error => {
                showMessage(`操作失败: ${error.message}`, 'danger');
                showStatus('操作失败');
            });
    }

    // 更新延长有效期预览
    function updateExtendPreview() {
        const days = parseInt(extendDaysInput.value) || 0;
        const hours = parseInt(extendHoursInput.value) || 0;
        const minutes = parseInt(extendMinutesInput.value) || 0;

        if (days === 0 && hours === 0 && minutes === 0) {
            extendPreview.style.display = 'none';
            return;
        }

        // 构建时间描述
        const timeParts = [];
        if (days > 0) timeParts.push(`${days}天`);
        if (hours > 0) timeParts.push(`${hours}小时`);
        if (minutes > 0) timeParts.push(`${minutes}分钟`);
        const timeDescription = timeParts.join('');

        // 如果有当前过期时间，计算预览时间
        const currentExpiresAt = infoExpiresAt.textContent;
        if (currentExpiresAt && currentExpiresAt !== '--') {
            try {
                const currentDate = new Date(currentExpiresAt);
                const newDate = new Date(currentDate);
                newDate.setDate(newDate.getDate() + days);
                newDate.setHours(newDate.getHours() + hours);
                newDate.setMinutes(newDate.getMinutes() + minutes);

                const newDateStr = newDate.toLocaleString('zh-CN', {
                    year: 'numeric',
                    month: '2-digit',
                    day: '2-digit',
                    hour: '2-digit',
                    minute: '2-digit',
                    second: '2-digit'
                });

                extendPreviewText.textContent = `延长${timeDescription}，新过期时间: ${newDateStr}`;
            } catch (e) {
                extendPreviewText.textContent = `将延长${timeDescription}`;
            }
        } else {
            extendPreviewText.textContent = `将延长${timeDescription}`;
        }

        extendPreview.style.display = 'block';
    }

    function toggleActive() {
        const apiKey = manageKeyInput.value.trim();
        
        if (!apiKey) {
            showMessage('请输入API Key', 'warning');
            return;
        }
        
        // 显示加载状态
        showStatus('正在切换状态...');
        
        // 发送请求
        fetch(`${APP_ROOT}/api/manage/toggle-active`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                key: apiKey
            })
        })
            .then(response => {
                if (!response.ok) {
                    throw new Error('操作失败');
                }
                return response.json();
            })
            .then(data => {
                if (data.error) {
                    showMessage(data.error, 'warning');
                    return;
                }
                
                const status = data.is_active ? '启用' : '禁用';
                showMessage(`成功将API Key ${status}`, 'success');
                
                // 刷新显示
                queryManageKey();
            })
            .catch(error => {
                showMessage(`操作失败: ${error.message}`, 'danger');
                showStatus('操作失败');
            });
    }

    function clearLastRequestTime() {
        const apiKey = manageKeyInput.value.trim();

        if (!apiKey) {
            showMessage('请输入API Key', 'warning');
            return;
        }

        // 显示确认对话框
        if (!confirm('确定要清除此API Key的最近换号时间吗？')) {
            return;
        }

        // 显示加载状态
        showStatus('正在清除最近换号时间...');

        // 发送请求
        fetch(`${APP_ROOT}/api/manage/clear-last-request`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                key: apiKey
            })
        })
            .then(response => {
                if (!response.ok) {
                    throw new Error('操作失败');
                }
                return response.json();
            })
            .then(data => {
                if (data.error) {
                    showMessage(data.error, 'warning');
                    return;
                }

                showMessage('成功清除最近换号时间', 'success');

                // 刷新显示
                queryManageKey();
            })
            .catch(error => {
                showMessage(`操作失败: ${error.message}`, 'danger');
                showStatus('操作失败');
            });
    }

    // 辅助函数
    function switchToApiKey(apiKey) {
        // 切换到API Key查询选项卡
        const apiKeyTab = document.getElementById('api-key-tab');
        bootstrap.Tab.getInstance(apiKeyTab).show();
        
        // 设置API Key并查询
        apiKeyInput.value = apiKey;
        queryApiKey();
    }
    
    function switchToManage() {
        // 切换到管理选项卡
        const manageTab = document.getElementById('manage-tab');
        bootstrap.Tab.getInstance(manageTab).show();
        
        // 设置API Key并查询
        manageKeyInput.value = currentApiKey;
        queryManageKey();
    }
    
    function showMessage(message, type = 'info') {
        // 在状态栏显示消息
        if (statusMessage) {
            statusMessage.textContent = message;
            statusMessage.className = '';
            switch (type) {
                case 'success':
                    statusMessage.classList.add('text-success');
                    break;
                case 'danger':
                case 'error':
                    statusMessage.classList.add('text-danger');
                    break;
                case 'warning':
                    statusMessage.classList.add('text-warning');
                    break;
                default:
                    statusMessage.classList.add('text-info');
            }
        }
        
        // 创建一个Toast通知
        const toastContainer = document.getElementById('toastContainer') || createToastContainer();
        const toastId = 'toast-' + Date.now();
        
        // 设置标题
        let title = '提示';
        if (type === 'success') title = '成功';
        else if (type === 'warning') title = '警告';
        else if (type === 'danger' || type === 'error') title = '错误';
        
        const toastHTML = `
            <div id="${toastId}" class="toast" role="alert" aria-live="assertive" aria-atomic="true">
                <div class="toast-header ${getToastHeaderClass(type)}">
                    <strong class="me-auto">${title}</strong>
                    <button type="button" class="btn-close" data-bs-dismiss="toast" aria-label="Close"></button>
                </div>
                <div class="toast-body">
                    ${message}
                </div>
            </div>
        `;
        
        toastContainer.insertAdjacentHTML('beforeend', toastHTML);
        const toastElement = document.getElementById(toastId);
        const toast = new bootstrap.Toast(toastElement, { delay: 3000 });
        toast.show();
        
        // 自动移除toast元素
        toastElement.addEventListener('hidden.bs.toast', function () {
            toastElement.remove();
        });
    }
    
    // 创建Toast容器
    function createToastContainer() {
        const container = document.createElement('div');
        container.id = 'toastContainer';
        container.className = 'toast-container position-fixed bottom-0 end-0 p-3';
        container.style.zIndex = '1050';
        document.body.appendChild(container);
        return container;
    }
    
    // 获取Toast头部样式
    function getToastHeaderClass(type) {
        switch (type) {
            case 'success': return 'bg-success text-white';
            case 'danger':
            case 'error': return 'bg-danger text-white';
            case 'warning': return 'bg-warning';
            default: return 'bg-info text-white';
        }
    }
    
    // 将UTC时间转换为中国时区(UTC+8)
    function convertToChineseTime(utcTimeString) {
        if (!utcTimeString) return '';
        
        // 解析UTC时间字符串
        const utcDate = new Date(utcTimeString.replace(' ', 'T') + 'Z');
        
        // 转换为中国时区 (UTC+8)
        const options = { 
            year: 'numeric', 
            month: '2-digit', 
            day: '2-digit',
            hour: '2-digit', 
            minute: '2-digit', 
            second: '2-digit',
            hour12: false,
            timeZone: 'Asia/Shanghai'
        };
        
        return new Intl.DateTimeFormat('zh-CN', options).format(utcDate);
    }
    
    function showStatus(message) {
        statusMessage.textContent = message;
    }
}); 