from flask import Blueprint, request, jsonify, render_template, redirect, url_for, flash, session, current_app
import secrets
import hashlib
import traceback
from datetime import datetime, timedelta, timezone
from .db import get_db_connection, close_db_connection
from flask_login import login_user, logout_user, login_required, current_user
from .models import User
import os
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

main_bp = Blueprint('main', __name__)

# 获取应用根路径
def get_application_root():
    root = os.getenv('APPLICATION_ROOT', '/chaxun')
    logger.info(f"应用根路径: {root}")
    return root

@main_bp.before_request
def log_request_info():
    """记录请求信息"""
    logger.info(f"请求: {request.method} {request.path}")
    logger.info(f"请求头: Host={request.headers.get('Host')}, Referer={request.headers.get('Referer')}")
    logger.info(f"请求参数: {request.args}")
    if request.is_json:
        logger.info(f"JSON数据: {request.json}")

@main_bp.after_request
def after_request(response):
    """添加CORS头部"""
    response.headers.add('Access-Control-Allow-Origin', '*')
    response.headers.add('Access-Control-Allow-Headers', 'Content-Type,Authorization')
    response.headers.add('Access-Control-Allow-Methods', 'GET,PUT,POST,DELETE,OPTIONS')
    return response

@main_bp.route('/', methods=['GET'])
@login_required
def index():
    """主页"""
    logger.info("访问主页")
    return render_template('index.html')

@main_bp.route('/query', methods=['GET'])
@login_required
def query_tool():
    """查询工具页面"""
    logger.info("访问查询工具页面")
    return render_template('query.html')

@main_bp.route('/login', methods=['GET', 'POST'])
def login():
    """登录页面"""
    logger.info(f"访问登录页面: {request.method}")
    
    if request.method == 'POST':
        username = request.form.get('username')
        password = request.form.get('password')
        
        logger.info(f"尝试登录: 用户名={username}")
        
        user = User.authenticate(username, password)
        if user:
            login_user(user)
            logger.info(f"登录成功: 用户={username}")
            
            next_page = request.args.get('next')
            if next_page:
                # 确保next参数是相对路径
                if next_page.startswith('/') and not next_page.startswith('//'):
                    logger.info(f"重定向到: {next_page}")
                    return redirect(next_page)
            
            logger.info("重定向到查询工具页面")
            return redirect(url_for('main.query_tool'))
        else:
            logger.warning(f"登录失败: 用户名={username}")
            return render_template('login.html', error='用户名或密码错误')
    
    return render_template('login.html')

@main_bp.route('/logout')
@login_required
def logout():
    """登出"""
    logger.info("用户登出")
    logout_user()
    return redirect(url_for('main.login'))

# 查询工具API路由
@main_bp.route('/api/query/api-key', methods=['GET'])
@login_required
def query_api_key():
    """API Key查询API"""
    logger.info("调用API Key查询API")
    
    api_key = request.args.get('key', '')
    page = int(request.args.get('page', 1))
    page_size = int(request.args.get('page_size', 20))
    
    if not api_key:
        return jsonify({"error": "未提供API Key"}), 400
    
    try:
        conn = get_db_connection()
        
        # 查询API Key基本信息
        query = """
            SELECT id, total_quota, remaining, is_active, created_at, expires_at, daily_usage_limit, first_used_at
            FROM api_keys
            WHERE key_hash = :key_hash
        """
        result = conn.run(query, key_hash=api_key)
        
        if not result:
            return jsonify({"error": "未找到该API Key"}), 404
            
        api_key_id, total_quota, remaining, is_active, created_at, expires_at, daily_limit, first_used_at = result[0]
        
        # 计算实际过期时间
        if first_used_at is not None:
            # 如果已经使用过，计算动态过期时间
            delta_seconds = (expires_at - created_at).total_seconds()
            dynamic_expiry = first_used_at + timedelta(seconds=delta_seconds)
            actual_expires_at = dynamic_expiry
        else:
            # 如果尚未使用，使用原始过期时间
            actual_expires_at = expires_at
        
        # 获取总记录数
        count_query = """
            SELECT COUNT(*)
            FROM usage_logs
            WHERE api_key_id = :api_key_id
        """
        total_records = conn.run(count_query, api_key_id=api_key_id)[0][0]
        
        # 查询使用记录（带分页）
        offset = (page - 1) * page_size
        usage_query = """
            SELECT timestamp, used_cookie, client_ip
            FROM usage_logs
            WHERE api_key_id = :api_key_id
            ORDER BY timestamp DESC
            LIMIT :page_size OFFSET :offset
        """
        usage_records = conn.run(usage_query, api_key_id=api_key_id, page_size=page_size, offset=offset)
        
        # 格式化数据
        info = {
            "total_quota": total_quota,
            "remaining": remaining,
            "is_active": is_active,
            "created_at": created_at.strftime("%Y-%m-%d %H:%M:%S"),
            "expires_at": actual_expires_at.strftime("%Y-%m-%d %H:%M:%S"),
            "daily_limit": daily_limit,
            "first_used_at": first_used_at.strftime("%Y-%m-%d %H:%M:%S") if first_used_at else None
        }
        
        usage = []
        for record in usage_records:
            timestamp, cookie, ip = record
            usage.append({
                "timestamp": timestamp.strftime("%Y-%m-%d %H:%M:%S"),
                "cookie": cookie,
                "ip": ip
            })
        
        close_db_connection(conn)
        
        return jsonify({
            "info": info,
            "usage": usage,
            "total_records": total_records
        })
        
    except Exception as e:
        logger.error(f"查询API Key失败: {str(e)}")
        return jsonify({"error": f"查询失败: {str(e)}"}), 500

@main_bp.route('/api/query/cookie', methods=['GET'])
@login_required
def query_cookie():
    """Cookie查询API"""
    logger.info("调用Cookie查询API")
    
    cookie = request.args.get('cookie', '')
    page = int(request.args.get('page', 1))
    page_size = int(request.args.get('page_size', 20))
    
    if not cookie:
        return jsonify({"error": "未提供Cookie"}), 400
    
    try:
        conn = get_db_connection()
        
        # 获取总记录数
        count_query = """
            SELECT COUNT(*)
            FROM usage_logs ul
            JOIN api_keys ak ON ul.api_key_id = ak.id
            WHERE ul.used_cookie = :cookie
        """
        total_records = conn.run(count_query, cookie=cookie)[0][0]
        
        # 查询Cookie使用记录（带分页）
        offset = (page - 1) * page_size
        usage_query = """
            SELECT ul.timestamp, ak.key_hash, ul.client_ip
            FROM usage_logs ul
            JOIN api_keys ak ON ul.api_key_id = ak.id
            WHERE ul.used_cookie = :cookie
            ORDER BY ul.timestamp DESC
            LIMIT :page_size OFFSET :offset
        """
        usage_records = conn.run(usage_query, cookie=cookie, page_size=page_size, offset=offset)
        
        # 格式化数据
        usage = []
        for record in usage_records:
            timestamp, api_key, ip = record
            usage.append({
                "timestamp": timestamp.strftime("%Y-%m-%d %H:%M:%S"),
                "api_key": api_key,
                "ip": ip
            })
        
        close_db_connection(conn)
        
        return jsonify({
            "usage": usage,
            "total_records": total_records
        })
        
    except Exception as e:
        logger.error(f"查询Cookie失败: {str(e)}")
        return jsonify({"error": f"查询失败: {str(e)}"}), 500

@main_bp.route('/api/query/manage-key', methods=['GET'])
@login_required
def query_manage_key():
    """管理API Key查询API"""
    logger.info("调用管理API Key查询API")
    
    api_key = request.args.get('key', '')
    
    if not api_key:
        return jsonify({"error": "未提供API Key"}), 400
    
    try:
        conn = get_db_connection()
        
        query = """
            SELECT total_quota, remaining, is_active, created_at, expires_at, daily_usage_limit, first_used_at, last_cookie_request_at
            FROM api_keys
            WHERE key_hash = :key_hash
        """
        result = conn.run(query, key_hash=api_key)

        if not result:
            return jsonify({"error": "未找到该API Key"}), 404

        total_quota, remaining, is_active, created_at, expires_at, daily_limit, first_used_at, last_cookie_request_at = result[0]
        
        # 计算实际过期时间
        if first_used_at is not None:
            # 如果已经使用过，计算动态过期时间
            delta_seconds = (expires_at - created_at).total_seconds()
            dynamic_expiry = first_used_at + timedelta(seconds=delta_seconds)
            actual_expires_at = dynamic_expiry
        else:
            # 如果尚未使用，使用原始过期时间
            actual_expires_at = expires_at
        
        close_db_connection(conn)
        
        return jsonify({
            "total_quota": total_quota,
            "remaining": remaining,
            "is_active": is_active,
            "created_at": created_at.strftime("%Y-%m-%d %H:%M:%S"),
            "expires_at": actual_expires_at.strftime("%Y-%m-%d %H:%M:%S"),
            "daily_limit": daily_limit,
            "first_used_at": first_used_at.strftime("%Y-%m-%d %H:%M:%S") if first_used_at else None,
            "last_cookie_request_at": last_cookie_request_at.strftime("%Y-%m-%d %H:%M:%S") if last_cookie_request_at else None
        })
        
    except Exception as e:
        logger.error(f"查询管理API Key失败: {str(e)}")
        return jsonify({"error": f"查询失败: {str(e)}"}), 500

@main_bp.route('/api/manage/add-quota', methods=['POST'])
@login_required
def add_quota():
    """增加API Key配额API"""
    logger.info("调用增加API Key配额API")
    
    data = request.get_json()
    if not data:
        return jsonify({"error": "未接收到有效的JSON数据"}), 400
    
    api_key = data.get('key', '')
    quota = data.get('quota', 0)
    
    if not api_key:
        return jsonify({"error": "未提供API Key"}), 400
    
    if not quota or quota <= 0:
        return jsonify({"error": "无效的配额数量"}), 400
    
    try:
        conn = get_db_connection()
        
        query = """
            UPDATE api_keys
            SET total_quota = total_quota + :quota,
                remaining = remaining + :quota
            WHERE key_hash = :key_hash
            RETURNING total_quota, remaining
        """
        result = conn.run(query, quota=quota, key_hash=api_key)
        
        if not result:
            return jsonify({"error": "未找到该API Key"}), 404
            
        close_db_connection(conn)
        
        return jsonify({
            "success": True,
            "message": f"成功增加配额 {quota}"
        })
        
    except Exception as e:
        logger.error(f"增加API Key配额失败: {str(e)}")
        return jsonify({"error": f"操作失败: {str(e)}"}), 500

@main_bp.route('/api/manage/set-daily-limit', methods=['POST'])
@login_required
def set_daily_limit():
    """设置API Key每日限制API"""
    logger.info("调用设置API Key每日限制API")
    
    data = request.get_json()
    if not data:
        return jsonify({"error": "未接收到有效的JSON数据"}), 400
    
    api_key = data.get('key', '')
    limit = data.get('limit', 0)
    
    if not api_key:
        return jsonify({"error": "未提供API Key"}), 400
    
    if limit < 0:
        return jsonify({"error": "无效的每日限制"}), 400
    
    try:
        conn = get_db_connection()
        
        query = """
            UPDATE api_keys
            SET daily_usage_limit = :limit
            WHERE key_hash = :key_hash
            RETURNING daily_usage_limit
        """
        result = conn.run(query, limit=limit, key_hash=api_key)
        
        if not result:
            return jsonify({"error": "未找到该API Key"}), 404
            
        close_db_connection(conn)
        
        return jsonify({
            "success": True,
            "message": f"成功设置每日限制为 {limit}"
        })
        
    except Exception as e:
        logger.error(f"设置API Key每日限制失败: {str(e)}")
        return jsonify({"error": f"操作失败: {str(e)}"}), 500

@main_bp.route('/api/manage/extend-expiry', methods=['POST'])
@login_required
def extend_expiry():
    """延长API Key有效期API - 支持天、小时、分钟"""
    logger.info("调用延长API Key有效期API")

    data = request.get_json()
    if not data:
        return jsonify({"error": "未接收到有效的JSON数据"}), 400

    api_key = data.get('key', '')
    days = int(data.get('days', 0))
    hours = int(data.get('hours', 0))
    minutes = int(data.get('minutes', 0))

    if not api_key:
        return jsonify({"error": "未提供API Key"}), 400

    # 检查是否至少有一个时间值
    if days == 0 and hours == 0 and minutes == 0:
        return jsonify({"error": "请至少输入一个时间值"}), 400
    
    try:
        conn = get_db_connection()

        # 计算总的分钟数，然后构建interval表达式
        total_minutes = days * 24 * 60 + hours * 60 + minutes
        interval_expr = f"{total_minutes} minutes"

        query = """
            UPDATE api_keys
            SET expires_at = expires_at + :interval::interval
            WHERE key_hash = :key_hash
            RETURNING expires_at
        """
        result = conn.run(query, interval=interval_expr, key_hash=api_key)

        if not result:
            return jsonify({"error": "未找到该API Key"}), 404

        close_db_connection(conn)

        # 构建友好的消息
        time_parts = []
        if days > 0:
            time_parts.append(f"{days}天")
        if hours > 0:
            time_parts.append(f"{hours}小时")
        if minutes > 0:
            time_parts.append(f"{minutes}分钟")
        time_message = "".join(time_parts)

        return jsonify({
            "success": True,
            "message": f"成功延长有效期 {time_message}"
        })
        
    except Exception as e:
        logger.error(f"延长API Key有效期失败: {str(e)}")
        return jsonify({"error": f"操作失败: {str(e)}"}), 500

@main_bp.route('/api/manage/toggle-active', methods=['POST'])
@login_required
def toggle_active():
    """切换API Key状态API"""
    logger.info("调用切换API Key状态API")
    
    data = request.get_json()
    if not data:
        return jsonify({"error": "未接收到有效的JSON数据"}), 400
    
    api_key = data.get('key', '')
    
    if not api_key:
        return jsonify({"error": "未提供API Key"}), 400
    
    try:
        conn = get_db_connection()
        
        query = """
            UPDATE api_keys
            SET is_active = NOT is_active
            WHERE key_hash = :key_hash
            RETURNING is_active
        """
        result = conn.run(query, key_hash=api_key)
        
        if not result:
            return jsonify({"error": "未找到该API Key"}), 404
            
        is_active = result[0][0]
        
        close_db_connection(conn)
        
        return jsonify({
            "success": True,
            "is_active": is_active,
            "message": f"成功将API Key {'启用' if is_active else '禁用'}"
        })
        
    except Exception as e:
        logger.error(f"切换API Key状态失败: {str(e)}")
        return jsonify({"error": f"操作失败: {str(e)}"}), 500

@main_bp.route('/api/manage/clear-last-request', methods=['POST'])
@login_required
def clear_last_request():
    """清除API Key最近换号时间API"""
    logger.info("调用清除API Key最近换号时间API")

    data = request.get_json()
    if not data:
        return jsonify({"error": "未接收到有效的JSON数据"}), 400

    api_key = data.get('key', '')

    if not api_key:
        return jsonify({"error": "未提供API Key"}), 400

    try:
        conn = get_db_connection()

        query = """
            UPDATE api_keys
            SET last_cookie_request_at = NULL
            WHERE key_hash = :key_hash
            RETURNING id
        """
        result = conn.run(query, key_hash=api_key)

        if not result:
            return jsonify({"error": "未找到该API Key"}), 404

        close_db_connection(conn)

        return jsonify({
            "success": True,
            "message": "成功清除最近换号时间"
        })

    except Exception as e:
        logger.error(f"清除API Key最近换号时间失败: {str(e)}")
        return jsonify({"error": f"操作失败: {str(e)}"}), 500

@main_bp.route('/api/generate-keys', methods=['POST'])
@login_required
def generate_keys():
    """生成卡密API"""
    logger.info("调用生成卡密API")
    
    try:
        data = request.get_json()
        if not data:
            logger.error("未接收到JSON数据")
            return jsonify({"error": "未接收到有效的JSON数据"}), 400
        
        logger.info(f"接收到的数据: {data}")
        
        # 获取参数
        quota = int(data.get('quota', 2))
        expires_days = int(data.get('expires_days', 1))
        count = int(data.get('count', 1))
        
        logger.info(f"参数: quota={quota}, expires_days={expires_days}, count={count}")
        
        # 参数验证
        if quota < 1 or quota > 1000000:
            logger.warning(f"无效的配额: {quota}")
            return jsonify({"error": "配额必须在1到1000000之间"}), 400
        
        if expires_days < 0 or expires_days > 3650:
            logger.warning(f"无效的有效期: {expires_days}")
            return jsonify({"error": "有效期必须在0到3650天之间"}), 400
            
        if count < 1 or count > 1000:
            logger.warning(f"无效的生成数量: {count}")
            return jsonify({"error": "生成数量必须在1到1000之间"}), 400
        
        # 连接数据库
        logger.info("连接数据库")
        conn = get_db_connection()
        
        generated_keys = []
        expires_at = (datetime.now(timezone.utc) + timedelta(days=expires_days) 
                     if expires_days else datetime(2999, 12, 31, 23, 59, 59, tzinfo=timezone.utc))

        logger.info(f"过期时间设置为: {expires_at}")

        for i in range(count):
            api_key = secrets.token_hex(16)
            key_hash = hashlib.sha256(api_key.encode()).hexdigest()
            
            logger.info(f"生成卡密 {i+1}/{count}")
            
            # 使用 pg8000 执行 SQL
            try:
                result = conn.run(
                    """
                    INSERT INTO api_keys (
                        key_hash,
                        total_quota,
                        remaining,
                        is_active,
                        created_at,
                        expires_at,
                        daily_usage_limit
                    ) VALUES (
                        :key_hash, 
                        :total_quota, 
                        :remaining, 
                        :is_active, 
                        NOW(), 
                        :expires_at,
                        :daily_usage_limit
                    )
                    RETURNING id, expires_at
                    """,
                    key_hash=key_hash, 
                    total_quota=quota, 
                    remaining=quota, 
                    is_active=True, 
                    expires_at=expires_at,
                    daily_usage_limit=30
                )
                
                logger.info(f"卡密插入成功: id={result[0][0]}")
                
                key_data = {
                    "api_key": api_key,
                    "key_hash": key_hash,
                    "expires_at": expires_at.strftime("%Y-%m-%d %H:%M:%S"),
                    "total_quota": quota,
                    "remaining": quota,
                    "daily_limit": 30
                }
                generated_keys.append(key_data)
            except Exception as e:
                logger.error(f"SQL执行错误: {e}")
                raise

        # pg8000 自动提交事务
        logger.info("关闭数据库连接")
        close_db_connection(conn)
        
        response_data = {
            "success": True,
            "keys": generated_keys,
            "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        }
        
        logger.info(f"成功生成 {len(generated_keys)} 个卡密")
        return jsonify(response_data)
        
    except Exception as e:
        # 如果发生错误，关闭连接
        logger.error(f"生成卡密过程中出现错误: {str(e)}")
        if 'conn' in locals() and conn:
            close_db_connection(conn)
        
        return jsonify({"error": str(e)}), 500

@main_bp.route('/api/health', methods=['GET'])
def health_check():
    """健康检查API"""
    logger.info("健康检查API被调用")
    return jsonify({
        "status": "ok",
        "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
        "version": "2025-08-05-v2",  # 版本标识
        "features": ["cookie-stats"]  # 功能列表
    })

@main_bp.route('/api/stats/cookie-usage', methods=['GET'])
@login_required
def get_cookie_usage_stats():
    """获取Cookie使用统计API"""
    logger.info("调用Cookie使用统计API")

    try:
        conn = get_db_connection()
        logger.info("数据库连接成功")

        # 首先检查cookies表是否存在
        check_cookies_table_query = """
            SELECT EXISTS (
                SELECT FROM information_schema.tables
                WHERE table_schema = 'public'
                AND table_name = 'cookies'
            )
        """

        cookies_table_exists = conn.run(check_cookies_table_query)[0][0]
        logger.info(f"cookies表存在: {cookies_table_exists}")

        # 获取Cookie池总体统计
        if cookies_table_exists:
            pool_stats_query = """
                SELECT
                    COUNT(*) as total,
                    COUNT(CASE WHEN is_available = true THEN 1 END) as available,
                    COUNT(CASE WHEN is_available = false THEN 1 END) as used
                FROM cookies
            """
            pool_stats = conn.run(pool_stats_query)
            logger.info(f"Cookie池统计查询结果: {pool_stats}")

            if pool_stats:
                total, available, used = pool_stats[0]
            else:
                total, available, used = 0, 0, 0
        else:
            logger.warning("cookies表不存在，使用默认值")
            total, available, used = 0, 0, 0

        # 检查usage_logs表是否存在
        check_usage_logs_table_query = """
            SELECT EXISTS (
                SELECT FROM information_schema.tables
                WHERE table_schema = 'public'
                AND table_name = 'usage_logs'
            )
        """

        usage_logs_table_exists = conn.run(check_usage_logs_table_query)[0][0]
        logger.info(f"usage_logs表存在: {usage_logs_table_exists}")

        # 获取过去7天的每日使用统计
        formatted_daily_stats = []
        if usage_logs_table_exists:
            daily_stats_query = """
                SELECT
                    DATE(timestamp) as date,
                    COUNT(*) as count
                FROM usage_logs
                WHERE timestamp >= CURRENT_DATE - INTERVAL '7 days'
                GROUP BY DATE(timestamp)
                ORDER BY date DESC
            """
            daily_stats = conn.run(daily_stats_query)
            logger.info(f"每日统计查询结果: {len(daily_stats) if daily_stats else 0}条记录")

            # 计算总使用量用于百分比计算
            total_daily_usage = sum(stat[1] for stat in daily_stats) if daily_stats else 0

            # 格式化每日统计数据
            for date, count in daily_stats:
                percentage = (count / total_daily_usage * 100) if total_daily_usage > 0 else 0
                formatted_daily_stats.append({
                    "date": date.strftime("%Y-%m-%d"),
                    "count": count,
                    "percentage": round(percentage, 1)
                })
        else:
            logger.warning("usage_logs表不存在，返回空的每日统计")

        close_db_connection(conn)
        logger.info("数据库连接已关闭")

        result = {
            "success": True,
            "pool_stats": {
                "total": total or 0,
                "available": available or 0,
                "used": used or 0
            },
            "daily_stats": formatted_daily_stats,
            "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "debug_info": {
                "cookies_table_exists": cookies_table_exists,
                "usage_logs_table_exists": usage_logs_table_exists,
                "daily_records_count": len(formatted_daily_stats)
            }
        }

        logger.info(f"返回统计结果: {result}")
        return jsonify(result)

    except Exception as e:
        logger.error(f"获取Cookie使用统计失败: {str(e)}")
        logger.error(f"错误详情: {traceback.format_exc()}")

        # 返回详细的错误信息用于调试
        return jsonify({
            "success": False,
            "error": f"获取统计失败: {str(e)}",
            "error_type": type(e).__name__,
            "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        }), 500