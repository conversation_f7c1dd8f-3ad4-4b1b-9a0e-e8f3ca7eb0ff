#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试Cookie统计功能的脚本
"""

import sys
import os

# 添加卡密端路径到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '805服务器部分', '卡密端'))

try:
    from PyQt6.QtWidgets import QApplication
    from 查询工具 import QueryTool
    
    def main():
        """测试主函数"""
        print("🚀 启动Cookie统计功能测试...")
        
        app = QApplication(sys.argv)
        
        # 创建查询工具窗口
        window = QueryTool()
        
        # 连接标签页切换事件
        window.tabs.currentChanged.connect(window.on_tab_changed)
        
        # 直接切换到统计标签页（第4个标签，索引为3）
        window.tabs.setCurrentIndex(3)
        
        print("✅ 窗口已创建，显示Cookie统计标签页")
        print("📊 统计功能包括：")
        print("   - Cookie池总体统计（总数、可用、已用）")
        print("   - 过去7天每日使用统计")
        print("   - 自动刷新功能")
        print("   - 美观的界面设计")
        
        window.show()
        
        return app.exec()

    if __name__ == "__main__":
        sys.exit(main())
        
except ImportError as e:
    print(f"❌ 导入失败: {e}")
    print("请确保：")
    print("1. PyQt6已正确安装: pip install PyQt6")
    print("2. psycopg2已正确安装: pip install psycopg2-binary")
    print("3. 数据库连接配置正确")
    sys.exit(1)
except Exception as e:
    print(f"❌ 运行时错误: {e}")
    sys.exit(1)
