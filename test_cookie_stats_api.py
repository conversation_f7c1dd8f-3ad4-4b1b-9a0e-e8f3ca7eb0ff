#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试Cookie统计API的脚本
"""

import requests
import json
import sys
from datetime import datetime

def test_cookie_stats_api():
    """测试Cookie统计API"""
    print("🧪 测试Cookie统计API")
    print("=" * 50)
    
    # 配置
    base_url = "https://api2.naoy.me/chaxun"  # 你的卡密端地址
    api_endpoint = f"{base_url}/api/stats/cookie-usage"
    
    print(f"🌐 测试地址: {api_endpoint}")
    
    try:
        # 发送请求
        print("📡 发送API请求...")
        response = requests.get(api_endpoint, timeout=10)
        
        print(f"📊 响应状态码: {response.status_code}")
        print(f"📋 响应头: {dict(response.headers)}")
        
        if response.status_code == 200:
            try:
                data = response.json()
                print("✅ API调用成功！")
                print("\n📈 返回数据:")
                print(json.dumps(data, indent=2, ensure_ascii=False))
                
                # 解析数据
                if data.get("success"):
                    pool_stats = data.get("pool_stats", {})
                    daily_stats = data.get("daily_stats", [])
                    debug_info = data.get("debug_info", {})
                    
                    print(f"\n🏊 Cookie池统计:")
                    print(f"  总数: {pool_stats.get('total', 0)}")
                    print(f"  可用: {pool_stats.get('available', 0)}")
                    print(f"  已用: {pool_stats.get('used', 0)}")
                    
                    print(f"\n📅 每日统计 ({len(daily_stats)}条记录):")
                    for stat in daily_stats:
                        print(f"  {stat['date']}: {stat['count']}次 ({stat['percentage']}%)")
                    
                    print(f"\n🔍 调试信息:")
                    print(f"  cookies表存在: {debug_info.get('cookies_table_exists', 'unknown')}")
                    print(f"  usage_logs表存在: {debug_info.get('usage_logs_table_exists', 'unknown')}")
                    print(f"  每日记录数: {debug_info.get('daily_records_count', 'unknown')}")
                    
                else:
                    print("❌ API返回失败状态")
                    print(f"错误信息: {data.get('error', 'unknown')}")
                    
            except json.JSONDecodeError as e:
                print(f"❌ JSON解析失败: {e}")
                print(f"原始响应: {response.text}")
                
        elif response.status_code == 401:
            print("❌ 认证失败 - 需要登录")
            print("请确保已登录卡密端管理界面")
            
        elif response.status_code == 404:
            print("❌ API端点不存在")
            print("请检查API路径是否正确")
            
        else:
            print(f"❌ API调用失败: HTTP {response.status_code}")
            try:
                error_data = response.json()
                print(f"错误详情: {json.dumps(error_data, indent=2, ensure_ascii=False)}")
            except:
                print(f"错误响应: {response.text}")
                
    except requests.exceptions.ConnectionError:
        print("❌ 连接失败")
        print("请检查:")
        print("1. 卡密端服务是否正在运行")
        print("2. 服务器地址是否正确")
        print("3. 网络连接是否正常")
        
    except requests.exceptions.Timeout:
        print("❌ 请求超时")
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")

def test_with_session():
    """使用会话测试（模拟登录状态）"""
    print("\n🔐 测试带会话的API调用")
    print("=" * 50)
    
    base_url = "https://api2.naoy.me/chaxun"
    login_url = f"{base_url}/login"
    api_endpoint = f"{base_url}/api/stats/cookie-usage"
    
    # 创建会话
    session = requests.Session()
    
    try:
        # 先访问登录页面获取会话
        print("📡 获取登录页面...")
        login_page = session.get(login_url, timeout=10)
        print(f"登录页面状态: {login_page.status_code}")
        
        # 尝试登录（如果需要）
        login_data = {
            'username': 'imcycyc',  # 替换为实际用户名
            'password': 'Ming980913.'  # 替换为实际密码
        }
        
        print("🔑 尝试登录...")
        login_response = session.post(login_url, data=login_data, timeout=10)
        print(f"登录响应状态: {login_response.status_code}")
        
        # 调用API
        print("📡 调用Cookie统计API...")
        api_response = session.get(api_endpoint, timeout=10)
        
        print(f"📊 API响应状态: {api_response.status_code}")
        
        if api_response.status_code == 200:
            try:
                data = api_response.json()
                print("✅ 带会话的API调用成功！")
                print(f"数据预览: {json.dumps(data, indent=2, ensure_ascii=False)[:500]}...")
            except json.JSONDecodeError:
                print(f"❌ JSON解析失败，原始响应: {api_response.text[:200]}...")
        else:
            print(f"❌ API调用失败: {api_response.status_code}")
            print(f"响应内容: {api_response.text[:200]}...")
            
    except Exception as e:
        print(f"❌ 会话测试失败: {str(e)}")

def main():
    """主函数"""
    print("🚀 Cookie统计API测试工具")
    print(f"⏰ 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 测试1: 直接API调用
    test_cookie_stats_api()
    
    # 测试2: 带会话的API调用
    test_with_session()
    
    print("\n" + "=" * 50)
    print("🎯 测试完成！")
    print("\n💡 如果遇到问题，请检查:")
    print("1. 卡密端服务是否正在运行")
    print("2. 数据库连接是否正常")
    print("3. cookies和usage_logs表是否存在")
    print("4. 是否已正确登录管理界面")

if __name__ == "__main__":
    main()
